一、客户画像
客户基础信息
宝尚旅游  地址在 高新区万福2号楼18楼
行业属性
小型智能家居产品开发企业，核心业务为物联网设备研发与测试
网络规模
两层办公区域（总面积约 2500㎡，呈梯形结构：上边 40 米，下边 60 米，高 50 米），7 楼 100-150 人，6 楼约 30 人（仅一侧有办公区域）


物理拓扑覆盖：
暂时无法在飞书文档外展示此内容

二、网络架构与网络使用方法
- 物理覆盖：
  - 7 楼：核心办公区，100-150 人，呈梯形结构（上边 40 米，下边 60 米，高 50 米）
  - 6 楼：扩展办公区，约 30 人，仅一侧有办公区域，整体空间较空旷
- 设备部署：
  - 7 楼核心架构：NBR 设备（接入多条内外网宽带）→ 接入交换机 → 6 台 AP（安装于工位头顶，间隔约 20 米，启用漫游功能）
  - 6 楼扩展架构：从 7 楼 NBR 延伸部署，与 7 楼共用相同 SSID
  - 特殊区域覆盖：
    - 会议室：未单独安装 AP，依赖办公区 AP 信号覆盖
    - 老板办公室：单独部署 1 台 AP 保障使用体验
  - 服务器部署：挂接于 7 楼 NBR 设备，采用 " 链路备份" 保障高可用性（主链路中断时自动切换至内部网络备份链路）
  
设备与配置
- 核心设备：
  - NBR 设备：承担多链路接入、服务器挂载、网络隔离功能
  - AP 设备：采用 WiFi 6 型号，支持 2.4G 与 5G 双频段
- 关键配置：
  - 初始未配置限速策略（后已补充）
  - AP 信道采用默认自动选择，未进行人工规划
  - AP 灵敏度设置为 90%（较高水平），但漫游效果依旧不明显
  - 7 楼与 6 楼共用相同 SSID（未区分楼层 SSID）
  - NBR 设备由 IT 人员设置每周定时重启（固定为某一天凌晨 3 点），用户认为可提升整网流畅度

业务特点：
- 大量物联网设备（2.4G 网关）研发与测试
- 外部演示服务器运行（核心业务支撑，不可断网）
- 混合办公与测试场景，无线连接为主要接入方式

网络使用特征：
- 设备以无线连接为主（手机、PC 均通过无线网卡接入）
- 存在无线转有线的测试场景需求
- 网络优先级明确：服务器可用性 > 老板办公室网络 > 内部测试网络。BBR WAN口接入多条网络，有电信网，公司内网，测试网，内部内网
- IT 人员为非专业网络运维角色，依赖基础操作维护网络
- 员工设备接入习惯：手机主要连接 5G 频段（感觉更快，由于2.4G干扰导致），PC 根据网卡性能选择 2.4G 或 5G 频段
- 测试场景特殊需求：部分测试需将无线网络桥接至有线网络（曾通过小米路由器私接实现）
- 移动办公特征：员工存在跨楼层移动需求（7 楼与 6 楼间往返）

三、痛点梳理
暂时无法在飞书文档外展示此内容

四、未满足的需求
1. 提升无线网络问题排查的门槛：
  - 需要简单易操作的工具，帮助 IT 人员快速排查卡顿原因（如干扰源、设备冲突等），目前IT人员反馈无线问题没有办法排查，有线问题还可以使用ping工具等尝试排查
  - 需具备私接设备检测与告警功能，私放ssid，避免配置冲突影响网络

2. 优化的无线漫游体验不灵敏，主要体现在：
  - 需解决跨楼层（7 楼→6 楼）漫游切换问题（因 SSID 相同、信号衰减慢导致）
  - 相隔较近（20米）的两个AP间，漫游切换不灵敏，导致用户体验为网速慢
  - 2.4G 频段需解决干扰问题，保障测试设备与办公设备的稳定使用

3. 网速的优先级保障：
  - 需要轻量化的 QoS 配置功能，确保服务器（核心业务）与老板办公室网络的优先级
  - 需优化服务器链路备份机制的可靠性，降低断网风险

4. 适配非专业 IT 人员的运维需求/报告等：
  - 需要自动化功能（如信道自动优化、干扰自动规避）减少人工操作
  - 需提供直观的网络状态报告，帮助 IT 人员快速了解网络健康度
